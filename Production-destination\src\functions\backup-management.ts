/**
 * Backup Management Function
 * Handles system backup operations and data recovery
 * Migrated from old-arch/src/admin-service/backup/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Backup types and enums
enum BackupType {
  FULL = 'FULL',
  INCREMENTAL = 'INCREMENTAL',
  DIFFERENTIAL = 'DIFFERENTIAL',
  SELECTIVE = 'SELECTIVE'
}

enum BackupStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

enum BackupScope {
  ORGANIZATION = 'ORGANIZATION',
  PROJECT = 'PROJECT',
  USER = 'USER',
  SYSTEM = 'SYSTEM'
}

// Validation schemas
const createBackupSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  type: Joi.string().valid(...Object.values(BackupType)).required(),
  scope: Joi.string().valid(...Object.values(BackupScope)).required(),
  organizationId: Joi.string().uuid().when('scope', {
    is: Joi.valid(BackupScope.ORGANIZATION, BackupScope.PROJECT, BackupScope.USER),
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  projectId: Joi.string().uuid().when('scope', {
    is: BackupScope.PROJECT,
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  userId: Joi.string().uuid().when('scope', {
    is: BackupScope.USER,
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  includeData: Joi.object({
    documents: Joi.boolean().default(true),
    workflows: Joi.boolean().default(true),
    users: Joi.boolean().default(true),
    organizations: Joi.boolean().default(true),
    projects: Joi.boolean().default(true),
    activities: Joi.boolean().default(false),
    auditLogs: Joi.boolean().default(false),
    configurations: Joi.boolean().default(true)
  }).optional(),
  schedule: Joi.object({
    enabled: Joi.boolean().default(false),
    frequency: Joi.string().valid('daily', 'weekly', 'monthly').optional(),
    time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
    dayOfWeek: Joi.number().min(0).max(6).optional(),
    dayOfMonth: Joi.number().min(1).max(31).optional(),
    retentionDays: Joi.number().min(1).max(365).default(30)
  }).optional(),
  encryption: Joi.object({
    enabled: Joi.boolean().default(true),
    algorithm: Joi.string().valid('AES-256', 'AES-128').default('AES-256'),
    password: Joi.string().min(8).optional()
  }).optional()
});

const restoreBackupSchema = Joi.object({
  backupId: Joi.string().uuid().required(),
  restoreOptions: Joi.object({
    overwriteExisting: Joi.boolean().default(false),
    selectiveRestore: Joi.boolean().default(false),
    restoreData: Joi.object({
      documents: Joi.boolean().default(true),
      workflows: Joi.boolean().default(true),
      users: Joi.boolean().default(true),
      organizations: Joi.boolean().default(true),
      projects: Joi.boolean().default(true),
      activities: Joi.boolean().default(false),
      auditLogs: Joi.boolean().default(false),
      configurations: Joi.boolean().default(true)
    }).optional(),
    targetOrganizationId: Joi.string().uuid().optional(),
    targetProjectId: Joi.string().uuid().optional()
  }).optional()
});

interface CreateBackupRequest {
  name: string;
  description?: string;
  type: BackupType;
  scope: BackupScope;
  organizationId?: string;
  projectId?: string;
  userId?: string;
  includeData?: {
    documents?: boolean;
    workflows?: boolean;
    users?: boolean;
    organizations?: boolean;
    projects?: boolean;
    activities?: boolean;
    auditLogs?: boolean;
    configurations?: boolean;
  };
  schedule?: {
    enabled?: boolean;
    frequency?: string;
    time?: string;
    dayOfWeek?: number;
    dayOfMonth?: number;
    retentionDays?: number;
  };
  encryption?: {
    enabled?: boolean;
    algorithm?: string;
    password?: string;
  };
}

interface BackupJob {
  id: string;
  name: string;
  description?: string;
  type: BackupType;
  scope: BackupScope;
  status: BackupStatus;
  organizationId?: string;
  projectId?: string;
  userId?: string;
  includeData: {
    documents: boolean;
    workflows: boolean;
    users: boolean;
    organizations: boolean;
    projects: boolean;
    activities: boolean;
    auditLogs: boolean;
    configurations: boolean;
  };
  schedule?: any;
  encryption: {
    enabled: boolean;
    algorithm: string;
  };
  progress: {
    percentage: number;
    currentStep: string;
    itemsProcessed: number;
    totalItems: number;
    startedAt?: string;
    estimatedCompletion?: string;
  };
  results?: {
    backupPath?: string;
    fileSize?: number;
    itemCount?: number;
    completedAt?: string;
    downloadUrl?: string;
    expiresAt?: string;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

/**
 * Create backup handler
 */
export async function createBackup(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create backup started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkBackupAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to backup functions" }
      }, request);
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = createBackupSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const backupRequest: CreateBackupRequest = value;

    // Check backup limits
    const canCreate = await checkBackupLimits(user.tenantId || user.id);
    if (!canCreate.allowed) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: canCreate.reason }
      }, request);
    }

    // Create backup job
    const backupId = uuidv4();
    const now = new Date().toISOString();

    const backupJob: BackupJob = {
      id: backupId,
      name: backupRequest.name,
      description: backupRequest.description,
      type: backupRequest.type,
      scope: backupRequest.scope,
      status: BackupStatus.PENDING,
      organizationId: backupRequest.organizationId,
      projectId: backupRequest.projectId,
      userId: backupRequest.userId,
      includeData: {
        documents: true,
        workflows: true,
        users: true,
        organizations: true,
        projects: true,
        activities: false,
        auditLogs: false,
        configurations: true,
        ...backupRequest.includeData
      },
      schedule: backupRequest.schedule,
      encryption: {
        enabled: true,
        algorithm: 'AES-256',
        ...backupRequest.encryption
      },
      progress: {
        percentage: 0,
        currentStep: 'Initializing',
        itemsProcessed: 0,
        totalItems: 0
      },
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('backup-jobs', backupJob);

    // Start backup process asynchronously
    await startBackupProcess(backupJob);

    // Create audit log entry
    await db.createItem('audit-logs', {
      id: uuidv4(),
      eventType: 'BACKUP_CREATED',
      userId: user.id,
      organizationId: backupRequest.organizationId,
      description: `Backup job created: ${backupRequest.name}`,
      severity: 'MEDIUM',
      resourceType: 'backup-job',
      resourceId: backupId,
      details: {
        backupType: backupRequest.type,
        backupScope: backupRequest.scope,
        includeData: backupJob.includeData,
        isScheduled: !!backupRequest.schedule?.enabled
      },
      timestamp: now,
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'BackupJobCreated',
      aggregateId: backupId,
      aggregateType: 'BackupJob',
      version: 1,
      data: {
        backupJob,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: backupRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Backup job created successfully", {
      correlationId,
      backupId,
      backupName: backupRequest.name,
      backupType: backupRequest.type,
      backupScope: backupRequest.scope,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        backupId,
        name: backupRequest.name,
        type: backupRequest.type,
        scope: backupRequest.scope,
        status: BackupStatus.PENDING,
        estimatedDuration: estimateBackupDuration(backupRequest),
        createdAt: now,
        message: "Backup job created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create backup failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get backup status handler
 */
export async function getBackupStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const backupId = request.params.backupId;

  logger.info("Get backup status started", { correlationId, backupId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkBackupAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to backup functions" }
      }, request);
    }

    if (!backupId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Backup ID is required" }
      }, request);
    }

    // Get backup job
    const backupJob = await db.readItem('backup-jobs', backupId, backupId);
    if (!backupJob) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Backup job not found" }
      }, request);
    }

    const backupData = backupJob as any;

    logger.info("Backup status retrieved successfully", {
      correlationId,
      backupId,
      status: backupData.status,
      progress: backupData.progress?.percentage,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        backupId: backupData.id,
        name: backupData.name,
        type: backupData.type,
        scope: backupData.scope,
        status: backupData.status,
        progress: backupData.progress,
        results: backupData.results,
        createdAt: backupData.createdAt,
        updatedAt: backupData.updatedAt
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get backup status failed", {
      correlationId,
      backupId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkBackupAccess(user: any): Promise<boolean> {
  try {
    // Check if user has admin or backup role
    return user.roles?.includes('admin') || user.roles?.includes('backup_admin');
  } catch (error) {
    logger.error('Failed to check backup access', { error, userId: user.id });
    return false;
  }
}

async function checkBackupLimits(tenantId: string): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Check concurrent backup limit
    const activeBackupsQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.tenantId = @tenantId AND c.status IN (@running, @pending)';
    const activeBackupsResult = await db.queryItems('backup-jobs', activeBackupsQuery, [tenantId, BackupStatus.RUNNING, BackupStatus.PENDING]);
    const activeBackups = Number(activeBackupsResult[0]) || 0;

    const maxConcurrentBackups = 3; // Configurable limit
    if (activeBackups >= maxConcurrentBackups) {
      return {
        allowed: false,
        reason: `Maximum concurrent backups limit reached (${maxConcurrentBackups})`
      };
    }

    return { allowed: true };

  } catch (error) {
    logger.error('Failed to check backup limits', { error, tenantId });
    return { allowed: false, reason: 'Failed to check limits' };
  }
}

function estimateBackupDuration(backupRequest: CreateBackupRequest): string {
  // Simplified estimation - in production, use historical data
  let baseTimeMinutes = 30; // Base time for full backup

  if (backupRequest.type === BackupType.INCREMENTAL) {
    baseTimeMinutes = 10;
  } else if (backupRequest.type === BackupType.DIFFERENTIAL) {
    baseTimeMinutes = 20;
  }

  // Adjust based on scope
  if (backupRequest.scope === BackupScope.ORGANIZATION) {
    baseTimeMinutes *= 2;
  } else if (backupRequest.scope === BackupScope.SYSTEM) {
    baseTimeMinutes *= 5;
  }

  return `${baseTimeMinutes} minutes`;
}

async function startBackupProcess(backupJob: BackupJob): Promise<void> {
  try {
    // Update status to running
    const updatedJob = {
      ...backupJob,
      id: backupJob.id,
      status: BackupStatus.RUNNING,
      progress: {
        ...backupJob.progress,
        percentage: 5,
        currentStep: 'Starting backup process',
        startedAt: new Date().toISOString()
      },
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('backup-jobs', updatedJob);

    // Simulate backup processing (in production, this would be actual backup logic)
    setTimeout(async () => {
      try {
        const backupData = await performBackup(backupJob);
        const downloadUrl = await uploadBackupToStorage(backupJob, backupData);

        const completedJob = {
          ...updatedJob,
          id: backupJob.id,
          status: BackupStatus.COMPLETED,
          progress: {
            ...updatedJob.progress,
            percentage: 100,
            currentStep: 'Completed'
          },
          results: {
            backupPath: `backups/${backupJob.id}`,
            fileSize: backupData.length,
            itemCount: backupData.itemCount || 0,
            completedAt: new Date().toISOString(),
            downloadUrl,
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
          },
          updatedAt: new Date().toISOString()
        };

        await db.updateItem('backup-jobs', completedJob);

        logger.info('Backup completed successfully', { backupId: backupJob.id });

      } catch (error) {
        const failedJob = {
          ...updatedJob,
          id: backupJob.id,
          status: BackupStatus.FAILED,
          progress: {
            ...updatedJob.progress,
            currentStep: 'Failed'
          },
          updatedAt: new Date().toISOString()
        };
        await db.updateItem('backup-jobs', failedJob);

        logger.error('Backup failed', { backupId: backupJob.id, error });
      }
    }, 15000); // 15 second delay for demo

  } catch (error) {
    logger.error('Failed to start backup process', { backupId: backupJob.id, error });
  }
}

async function performBackup(backupJob: BackupJob): Promise<any> {
  try {
    logger.info('Starting production backup', {
      backupId: backupJob.id,
      backupType: backupJob.type,
      scope: backupJob.scope
    });

    const backupData: any = {
      backupId: backupJob.id,
      backupName: backupJob.name,
      backupType: backupJob.type,
      scope: backupJob.scope,
      createdAt: new Date().toISOString(),
      version: '1.0',
      metadata: {
        organizationId: backupJob.organizationId,
        createdBy: backupJob.createdBy,
        compressionType: 'gzip',
        encryptionEnabled: true
      }
    };

    // Production backup implementation based on scope
    switch (backupJob.scope) {
      case 'organization':
        backupData.data = await performOrganizationBackup(backupJob.organizationId);
        break;
      case 'project':
        backupData.data = await performProjectBackup(backupJob.projectId, backupJob.organizationId);
        break;
      case 'documents':
        backupData.data = await performDocumentsBackup(backupJob.organizationId, backupJob.projectId);
        break;
      case 'users':
        backupData.data = await performUsersBackup(backupJob.organizationId);
        break;
      case 'workflows':
        backupData.data = await performWorkflowsBackup(backupJob.organizationId, backupJob.projectId);
        break;
      case 'analytics':
        backupData.data = await performAnalyticsBackup(backupJob.organizationId, backupJob.projectId);
        break;
      default:
        throw new Error(`Unsupported backup scope: ${backupJob.scope}`);
    }

    // Calculate backup statistics
    backupData.statistics = {
      itemCount: Array.isArray(backupData.data) ? backupData.data.length : Object.keys(backupData.data).length,
      totalSize: JSON.stringify(backupData.data).length,
      compressionRatio: 0.7, // Estimated compression ratio
      encryptionOverhead: 0.1 // Estimated encryption overhead
    };

    // Compress and encrypt backup data
    const processedBackup = await processBackupData(backupData);

    logger.info('Production backup completed', {
      backupId: backupJob.id,
      itemCount: backupData.statistics.itemCount,
      originalSize: backupData.statistics.totalSize,
      processedSize: processedBackup.length
    });

    return processedBackup;

  } catch (error) {
    logger.error('Production backup failed', {
      error: error instanceof Error ? error.message : String(error),
      backupId: backupJob.id,
      scope: backupJob.scope
    });
    throw error;
  }
}

async function uploadBackupToStorage(backupJob: BackupJob, data: Buffer): Promise<string> {
  try {
    const blobServiceClient = new BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
    const containerClient = blobServiceClient.getContainerClient("backups");

    const fileName = `${backupJob.id}-${Date.now()}.backup`;
    const blobClient = containerClient.getBlobClient(fileName);

    await blobClient.getBlockBlobClient().upload(data, data.length);

    return blobClient.url;
  } catch (error) {
    logger.error('Failed to upload backup to storage', { backupId: backupJob.id, error });
    throw error;
  }
}

/**
 * Perform organization-wide backup
 */
async function performOrganizationBackup(organizationId: string): Promise<any> {
  try {
    const organizationData: any = {};

    // Backup organization details
    const organization = await db.readItem('organizations', organizationId, organizationId);
    organizationData.organization = organization;

    // Backup all projects
    const projectsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const projects = await db.queryItems('projects', projectsQuery, [{ name: '@orgId', value: organizationId }]);
    organizationData.projects = projects;

    // Backup all users
    const usersQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const users = await db.queryItems('users', usersQuery, [{ name: '@orgId', value: organizationId }]);
    organizationData.users = users.map(user => ({
      ...user,
      password: '[REDACTED]', // Remove sensitive data
      apiKeys: '[REDACTED]'
    }));

    // Backup all documents
    const documentsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const documents = await db.queryItems('documents', documentsQuery, [{ name: '@orgId', value: organizationId }]);
    organizationData.documents = documents;

    // Backup workflows
    const workflowsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const workflows = await db.queryItems('workflows', workflowsQuery, [{ name: '@orgId', value: organizationId }]);
    organizationData.workflows = workflows;

    // Backup templates
    const templatesQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const templates = await db.queryItems('templates', templatesQuery, [{ name: '@orgId', value: organizationId }]);
    organizationData.templates = templates;

    logger.info('Organization backup data collected', {
      organizationId,
      projects: projects.length,
      users: users.length,
      documents: documents.length,
      workflows: workflows.length,
      templates: templates.length
    });

    return organizationData;

  } catch (error) {
    logger.error('Failed to perform organization backup', {
      error: error instanceof Error ? error.message : String(error),
      organizationId
    });
    throw error;
  }
}

/**
 * Perform project-specific backup
 */
async function performProjectBackup(projectId: string, organizationId: string): Promise<any> {
  try {
    const projectData: any = {};

    // Backup project details
    const project = await db.readItem('projects', projectId, projectId);
    projectData.project = project;

    // Backup project documents
    const documentsQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.organizationId = @orgId';
    const documents = await db.queryItems('documents', documentsQuery, [
      { name: '@projectId', value: projectId },
      { name: '@orgId', value: organizationId }
    ]);
    projectData.documents = documents;

    // Backup project workflows
    const workflowsQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.organizationId = @orgId';
    const workflows = await db.queryItems('workflows', workflowsQuery, [
      { name: '@projectId', value: projectId },
      { name: '@orgId', value: organizationId }
    ]);
    projectData.workflows = workflows;

    // Backup project activities
    const activitiesQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.organizationId = @orgId';
    const activities = await db.queryItems('activities', activitiesQuery, [
      { name: '@projectId', value: projectId },
      { name: '@orgId', value: organizationId }
    ]);
    projectData.activities = activities;

    logger.info('Project backup data collected', {
      projectId,
      organizationId,
      documents: documents.length,
      workflows: workflows.length,
      activities: activities.length
    });

    return projectData;

  } catch (error) {
    logger.error('Failed to perform project backup', {
      error: error instanceof Error ? error.message : String(error),
      projectId,
      organizationId
    });
    throw error;
  }
}

/**
 * Perform documents backup
 */
async function performDocumentsBackup(organizationId: string, projectId?: string): Promise<any> {
  try {
    let documentsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const parameters = [{ name: '@orgId', value: organizationId }];

    if (projectId) {
      documentsQuery += ' AND c.projectId = @projectId';
      parameters.push({ name: '@projectId', value: projectId });
    }

    const documents = await db.queryItems('documents', documentsQuery, parameters);

    // Include document metadata and content references
    const documentsWithMetadata = documents.map(doc => ({
      ...doc,
      backupMetadata: {
        backedUpAt: new Date().toISOString(),
        originalSize: doc.size,
        contentLocation: doc.blobPath || doc.url
      }
    }));

    logger.info('Documents backup data collected', {
      organizationId,
      projectId,
      documentCount: documents.length
    });

    return documentsWithMetadata;

  } catch (error) {
    logger.error('Failed to perform documents backup', {
      error: error instanceof Error ? error.message : String(error),
      organizationId,
      projectId
    });
    throw error;
  }
}

/**
 * Perform users backup
 */
async function performUsersBackup(organizationId: string): Promise<any> {
  try {
    const usersQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const users = await db.queryItems('users', usersQuery, [{ name: '@orgId', value: organizationId }]);

    // Remove sensitive information from backup
    const sanitizedUsers = users.map(user => ({
      ...user,
      password: '[REDACTED]',
      apiKeys: '[REDACTED]',
      refreshTokens: '[REDACTED]',
      backupMetadata: {
        backedUpAt: new Date().toISOString(),
        sensitiveDataRemoved: true
      }
    }));

    logger.info('Users backup data collected', {
      organizationId,
      userCount: users.length
    });

    return sanitizedUsers;

  } catch (error) {
    logger.error('Failed to perform users backup', {
      error: error instanceof Error ? error.message : String(error),
      organizationId
    });
    throw error;
  }
}

/**
 * Perform workflows backup
 */
async function performWorkflowsBackup(organizationId: string, projectId?: string): Promise<any> {
  try {
    let workflowsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const parameters = [{ name: '@orgId', value: organizationId }];

    if (projectId) {
      workflowsQuery += ' AND c.projectId = @projectId';
      parameters.push({ name: '@projectId', value: projectId });
    }

    const workflows = await db.queryItems('workflows', workflowsQuery, parameters);

    // Include workflow execution history
    const workflowsWithHistory = await Promise.all(workflows.map(async (workflow) => {
      const executionsQuery = 'SELECT * FROM c WHERE c.workflowId = @workflowId';
      const executions = await db.queryItems('workflow-executions', executionsQuery, [
        { name: '@workflowId', value: workflow.id }
      ]);

      return {
        ...workflow,
        executionHistory: executions,
        backupMetadata: {
          backedUpAt: new Date().toISOString(),
          executionCount: executions.length
        }
      };
    }));

    logger.info('Workflows backup data collected', {
      organizationId,
      projectId,
      workflowCount: workflows.length
    });

    return workflowsWithHistory;

  } catch (error) {
    logger.error('Failed to perform workflows backup', {
      error: error instanceof Error ? error.message : String(error),
      organizationId,
      projectId
    });
    throw error;
  }
}

/**
 * Perform analytics backup
 */
async function performAnalyticsBackup(organizationId: string, projectId?: string): Promise<any> {
  try {
    let analyticsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const parameters = [{ name: '@orgId', value: organizationId }];

    if (projectId) {
      analyticsQuery += ' AND c.projectId = @projectId';
      parameters.push({ name: '@projectId', value: projectId });
    }

    // Backup analytics events
    const analyticsEvents = await db.queryItems('analytics-events', analyticsQuery, parameters);

    // Backup aggregated analytics data
    const aggregatedAnalytics = await db.queryItems('analytics-aggregated', analyticsQuery, parameters);

    // Backup user activity logs
    const activityLogs = await db.queryItems('activities', analyticsQuery, parameters);

    const analyticsData = {
      events: analyticsEvents,
      aggregated: aggregatedAnalytics,
      activities: activityLogs,
      backupMetadata: {
        backedUpAt: new Date().toISOString(),
        eventsCount: analyticsEvents.length,
        aggregatedCount: aggregatedAnalytics.length,
        activitiesCount: activityLogs.length
      }
    };

    logger.info('Analytics backup data collected', {
      organizationId,
      projectId,
      eventsCount: analyticsEvents.length,
      aggregatedCount: aggregatedAnalytics.length,
      activitiesCount: activityLogs.length
    });

    return analyticsData;

  } catch (error) {
    logger.error('Failed to perform analytics backup', {
      error: error instanceof Error ? error.message : String(error),
      organizationId,
      projectId
    });
    throw error;
  }
}

/**
 * Process backup data with compression and encryption
 */
async function processBackupData(backupData: any): Promise<Buffer> {
  try {
    // Convert to JSON string
    const jsonData = JSON.stringify(backupData, null, 2);

    // Compress using gzip
    const zlib = require('zlib');
    const compressed = zlib.gzipSync(Buffer.from(jsonData, 'utf8'));

    // Encrypt the compressed data
    const crypto = require('crypto');
    const algorithm = 'aes-256-gcm';
    const key = crypto.randomBytes(32); // In production, use proper key management
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipher(algorithm, key);
    let encrypted = cipher.update(compressed);
    encrypted = Buffer.concat([encrypted, cipher.final()]);

    // Create final backup package with metadata
    const backupPackage = {
      version: '1.0',
      algorithm,
      compressed: true,
      encrypted: true,
      iv: iv.toString('base64'),
      data: encrypted.toString('base64'),
      metadata: {
        originalSize: jsonData.length,
        compressedSize: compressed.length,
        encryptedSize: encrypted.length,
        processedAt: new Date().toISOString()
      }
    };

    logger.info('Backup data processed', {
      originalSize: jsonData.length,
      compressedSize: compressed.length,
      encryptedSize: encrypted.length,
      compressionRatio: (compressed.length / jsonData.length * 100).toFixed(2) + '%'
    });

    return Buffer.from(JSON.stringify(backupPackage, null, 2));

  } catch (error) {
    logger.error('Failed to process backup data', {
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

// Register functions
app.http('backup-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'management/backups/create',
  handler: createBackup
});

app.http('backup-status', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'management/backups/{backupId}/status',
  handler: getBackupStatus
});
